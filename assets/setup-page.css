/* Setup Page Specific Styles */

.setup-breadcrumb {
    margin-bottom: 20px;
    padding: 10px;
    background: #f9f9f9;
    border-left: 4px solid #0073aa;
    border-radius: 4px;
}

.setup-breadcrumb p {
    margin: 0;
}

.hidden {
    display: none;
}

/* Step Progress Indicator */
.setup-progress-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 0 20px;
}

.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.step-indicator.active {
    opacity: 1;
}

.step-indicator.completed {
    opacity: 1;
}

.step-indicator:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 60%;
    right: -40%;
    height: 2px;
    background: #ddd;
    z-index: 1;
}

.step-indicator.completed:not(:last-child)::after {
    background: #0073aa;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #ddd;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.step-indicator.active .step-number {
    background: #0073aa;
    color: white;
}

.step-indicator.completed .step-number {
    background: #00a32a;
    color: white;
}

.step-title {
    font-size: 12px;
    text-align: center;
    color: #666;
    font-weight: 500;
}

.step-indicator.active .step-title {
    color: #0073aa;
    font-weight: 600;
}

.step-indicator.completed .step-title {
    color: #00a32a;
    font-weight: 600;
}

.setup-step {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    display: none;
}

.setup-step.active {
    display: block;
}

.setup-step h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#setup-status {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.status-item {
    margin-bottom: 10px;
    padding: 5px 10px;
    border-radius: 3px;
}

.status-item.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-item.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-item.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

#git-path-status {
    margin-top: 10px;
}

.progress-item {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 3px;
    background: #f1f1f1;
    border: 1px solid #ddd;
}

.progress-item.in-progress {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.progress-item.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.progress-item.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Setup Navigation */
.setup-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.setup-navigation .button {
    min-width: 120px;
}

.setup-navigation .button:only-child {
    margin-left: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .setup-progress-indicator {
        flex-direction: column;
        gap: 10px;
    }

    .step-indicator:not(:last-child)::after {
        display: none;
    }

    .setup-navigation {
        flex-direction: column;
        gap: 10px;
    }

    .setup-navigation .button {
        width: 100%;
    }
}
