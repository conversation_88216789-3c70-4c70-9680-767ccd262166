/**
 * Git Manager Setup Page JavaScript
 */
jQuery(document).ready(function($) {
    'use strict';
    
    const SetupPage = {
        currentStep: 1,

        init: function() {
            this.bindEvents();
            this.showStep(1);
        },

        bindEvents: function() {
            $('#test-git').on('click', this.handleTestGit);
            $('#check-setup').on('click', this.handleCheckSetup);
            $('#start-setup').on('click', this.handleStartSetup);
            $('#complete-setup').on('click', this.handleCompleteSetup);

            // Step navigation
            $('#next-step-1').on('click', function () { SetupPage.nextStep(); });
            $('#prev-step-2').on('click', function () { SetupPage.prevStep(); });
            $('#prev-step-2-progress').on('click', function () { SetupPage.prevStep(); });
            $('#prev-step-3').on('click', function () { SetupPage.prevStep(); });
        },

        showStep: function (stepNumber) {
            // Hide all steps
            $('.setup-step').removeClass('active');
            $('.step-indicator').removeClass('active completed');

            // Show current step
            $('#step-' + stepNumber).addClass('active');

            // Update progress indicator
            for (let i = 1; i <= 3; i++) {
                if (i < stepNumber) {
                    $('.step-indicator[data-step="' + i + '"]').addClass('completed');
                } else if (i === stepNumber) {
                    $('.step-indicator[data-step="' + i + '"]').addClass('active');
                }
            }

            this.currentStep = stepNumber;
        },

        nextStep: function () {
            if (this.currentStep < 3) {
                this.showStep(this.currentStep + 1);
            }
        },

        prevStep: function () {
            if (this.currentStep > 1) {
                this.showStep(this.currentStep - 1);
            }
        },

        handleTestGit: function() {
            var gitPath = $('#git-path').val();
            var button = $(this);

            button.prop('disabled', true).text('Testing...');

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'git_path',
                    git_path: gitPath
                })
                .done(function(response) {
                    if (response.success) {
                        $('#git-path-status').html('<div class="notice notice-success inline"><p>✓ Git found: ' + response.data.version + '</p></div>');
                        $('#check-setup').show();
                        $('#next-step-1').show();
                    } else {
                        $('#git-path-status').html('<div class="notice notice-error inline"><p>✗ ' + response.data + '</p></div>');
                        $('#check-setup').hide();
                        $('#next-step-1').hide();
                    }
                })
                .fail(function() {
                    $('#git-path-status').html('<div class="notice notice-error inline"><p>✗ Failed to test Git path</p></div>');
                    $('#check-setup').hide();
                    $('#next-step-1').hide();
                })
                .always(function() {
                    button.prop('disabled', false).text('Test Git Path');
                });
        },

        handleCheckSetup: function() {
            var gitPath = $('#git-path').val();
            var repoPath = $('#repo-path').val();
            var button = $(this);

            button.prop('disabled', true).text('Checking...');

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'full_status',
                    git_path: gitPath,
                    repo_path: repoPath
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.displaySetupStatus(response.data);
                        SetupPage.showStep(2);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('Check Setup Status');
                });
        },

        displaySetupStatus: function(status) {
            var html = '<h3>Setup Status</h3>';

            html += '<div class="status-item ' + (status.git_available ? 'success' : 'error') + '">';
            html += (status.git_available ? '✓' : '✗') + ' Git Available</div>';

            html += '<div class="status-item ' + (status.repo_exists ? 'success' : 'warning') + '">';
            html += (status.repo_exists ? '✓' : '○') + ' Git Repository</div>';

            html += '<div class="status-item ' + (status.user_configured ? 'success' : 'warning') + '">';
            html += (status.user_configured ? '✓' : '○') + ' User Configuration</div>';

            html += '<div class="status-item ' + (status.has_remote ? 'success' : 'warning') + '">';
            html += (status.has_remote ? '✓' : '○') + ' Remote Repository</div>';

            html += '<div class="status-item ' + (status.has_branch ? 'success' : 'warning') + '">';
            html += (status.has_branch ? '✓' : '○') + ' Initial Branch</div>';

            $('#setup-status').html(html);

            // If setup is already complete, show step 3
            if (status.is_complete) {
                SetupPage.showStep(3);
            }
        },

        updateProgress: function(step, status, message) {
            var element = $('#progress-' + step);
            element.removeClass('in-progress success error');

            if (status === 'in-progress') {
                element.addClass('in-progress').text('⟳ ' + message);
            } else if (status === 'success') {
                element.addClass('success').text('✓ ' + message);
            } else if (status === 'error') {
                element.addClass('error').text('✗ ' + message);
            }
        },

        handleStartSetup: function() {
            var userName = $('#git-user-name').val();
            var userEmail = $('#git-user-email').val();
            var branchName = $('#branch-name').val();
            var remoteName = $('#remote-name').val();
            var remoteUrl = $('#remote-url').val();

            if (!userName || !userEmail || !branchName) {
                alert('Please fill in all required fields (User Name, User Email, and Branch Name)');
                return;
            }

            var button = $(this);
            button.prop('disabled', true).text('Setting up...');

            // Hide form and show progress
            $('#config-form').hide();
            $('#setup-progress').show();

            // Start the setup process
            SetupPage.startSetupProcess({
                gitPath: $('#git-path').val(),
                repoPath: $('#repo-path').val(),
                userName: userName,
                userEmail: userEmail,
                branchName: branchName,
                remoteName: remoteName,
                remoteUrl: remoteUrl
            });
        },

        startSetupProcess: function(config) {
            // Step 1: Initialize repository with branch
            SetupPage.updateProgress('init', 'in-progress', 'Initializing repository with branch "' + config.branchName + '"...');

            $.post(ajaxurl, {
                    action: 'git_init_repo_with_branch',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    branch_name: config.branchName
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.updateProgress('init', 'success', 'Repository initialized with branch "' + config.branchName + '"');
                        SetupPage.setupUserConfig(config);
                    } else {
                        SetupPage.updateProgress('init', 'error', 'Failed to initialize repository: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    SetupPage.updateProgress('init', 'error', 'Failed to initialize repository');
                    SetupPage.resetSetupForm();
                });
        },

        setupUserConfig: function(config) {
            SetupPage.updateProgress('user', 'in-progress', 'Setting user configuration...');

            $.post(ajaxurl, {
                    action: 'git_set_user',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    user_name: config.userName,
                    user_email: config.userEmail
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.updateProgress('user', 'success', 'User configuration set');
                        if (config.remoteUrl) {
                            SetupPage.setupRemote(config);
                        } else {
                            SetupPage.updateProgress('remote', 'success', 'Remote setup skipped');
                            SetupPage.createInitialFiles(config);
                        }
                    } else {
                        SetupPage.updateProgress('user', 'error', 'Failed to set user configuration: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    SetupPage.updateProgress('user', 'error', 'Failed to set user configuration');
                    SetupPage.resetSetupForm();
                });
        },

        setupRemote: function(config) {
            SetupPage.updateProgress('remote', 'in-progress', 'Adding remote repository...');

            $.post(ajaxurl, {
                    action: 'git_add_remote',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    remote_name: config.remoteName,
                    remote_url: config.remoteUrl
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.updateProgress('remote', 'success', 'Remote repository added');
                        SetupPage.createInitialFiles(config);
                    } else {
                        SetupPage.updateProgress('remote', 'error', 'Failed to add remote: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    SetupPage.updateProgress('remote', 'error', 'Failed to add remote repository');
                    SetupPage.resetSetupForm();
                });
        },

        createInitialFiles: function(config) {
            SetupPage.updateProgress('files', 'in-progress', 'Creating initial files and commit...');

            $.post(ajaxurl, {
                    action: 'git_create_initial_commit',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    branch_name: config.branchName
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.updateProgress('files', 'success', 'Initial files created and committed');
                        SetupPage.completeSetup(config);
                    } else {
                        SetupPage.updateProgress('files', 'error', 'Failed to create initial files: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    SetupPage.updateProgress('files', 'error', 'Failed to create initial files');
                    SetupPage.resetSetupForm();
                });
        },

        completeSetup: function(config) {
            // Save all settings
            var settings = {
                git_path: config.gitPath,
                repo_path: config.repoPath,
                remote_name: config.remoteName || 'origin',
                branch_name: config.branchName,
                auto_add: '1'
            };

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'save_settings',
                    settings: settings
                })
                .done(function(response) {
                    if (response.success) {
                        setTimeout(function() {
                            SetupPage.showStep(3);
                        }, 1000);
                    } else {
                        alert('Error saving settings: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    alert('Failed to save settings');
                    SetupPage.resetSetupForm();
                });
        },

        resetSetupForm: function() {
            $('#setup-progress').hide();
            $('#config-form').show();
            $('#start-setup').prop('disabled', false).text('Initialize Repository');
        },

        handleCompleteSetup: function() {
            var settings = {
                git_path: $('#git-path').val(),
                repo_path: $('#repo-path').val(),
                remote_name: $('#remote-name').val() || 'origin',
                branch_name: $('#branch-name').val() || 'main',
                auto_add: $('#auto-add').is(':checked') ? '1' : '0'
            };

            // Create .gitignore if content provided
            var gitignoreContent = $('#gitignore-content').val();
            if (gitignoreContent) {
                settings.gitignore_content = gitignoreContent;
            }

            var button = $(this);
            button.prop('disabled', true).text('Completing...');

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'save_settings',
                    settings: settings
                })
                .done(function(response) {
                    if (response.success) {
                        // Hide all steps and show completion
                        $('.setup-step').removeClass('active');
                        $('.step-indicator').removeClass('active').addClass('completed');
                        $('#setup-complete').show();
                    } else {
                        alert('Error saving settings: ' + response.data);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('Complete Setup');
                });
        }
    };

    // Initialize SetupPage
    SetupPage.init();
});
