/**
 * Git Manager Utility Functions
 * Common AJAX and UI utilities to reduce code duplication
 */

window.GitUtils = (function($) {
    'use strict';

    return {
        /**
         * Make an AJAX request with standardized error handling
         * @param {string} action - The AJAX action name
         * @param {object} data - Additional data to send
         * @param {function} successCallback - Success callback function
         * @param {function} errorCallback - Optional error callback function
         * @param {string} loadingMessage - Optional loading message
         */
        ajaxRequest: function(action, data, successCallback, errorCallback, loadingMessage) {
            data = data || {};
            data.action = action;
            data.nonce = wpGitManager.nonce;

            if (loadingMessage) {
                this.showLoading(loadingMessage);
            }

            $.post(wpGitManager.ajax_url, data)
                .done(function(response) {
                    if (loadingMessage) {
                        GitUtils.hideLoading();
                    }
                    
                    if (response.success) {
                        if (successCallback) {
                            successCallback(response);
                        }
                    } else {
                        const errorMsg = 'Error: ' + (response.data || 'Unknown error');
                        if (errorCallback) {
                            errorCallback(errorMsg, response);
                        } else {
                            GitUtils.showMessage(errorMsg, 'error');
                        }
                    }
                })
                .fail(function(xhr, status, error) {
                    if (loadingMessage) {
                        GitUtils.hideLoading();
                    }
                    
                    const errorMsg = 'AJAX Error: ' + error + ' (Status: ' + xhr.status + ')';
                    console.error('AJAX Error:', xhr.responseText);
                    
                    if (errorCallback) {
                        errorCallback(errorMsg, xhr);
                    } else {
                        GitUtils.showMessage(errorMsg, 'error');
                    }
                });
        },

        /**
         * Show loading message in the output area
         * @param {string} message - Loading message to display
         */
        showLoading: function(message) {
            const outputElement = $('#git-output');
            if (outputElement.length) {
                outputElement.html('<div class="notice notice-info"><p>' + message + '</p></div>');
            }
        },

        /**
         * Hide loading message
         */
        hideLoading: function() {
            const outputElement = $('#git-output');
            if (outputElement.length) {
                outputElement.empty();
            }
        },

        /**
         * Show a message in the output area
         * @param {string} message - Message to display
         * @param {string} type - Message type ('success', 'error', 'warning', 'info')
         */
        showMessage: function(message, type) {
            type = type || 'info';
            const className = 'notice-' + type;
            const outputElement = $('#git-output');
            
            if (outputElement.length) {
                outputElement.html('<div class="notice ' + className + ' is-dismissible"><p>' + message + '</p></div>');
            } else {
                // Fallback to WordPress admin notices
                const notice = $('<div class="notice ' + className + ' is-dismissible"><p>' + message + '</p></div>');
                $('.wrap').prepend(notice);
                
                setTimeout(function() {
                    notice.fadeOut();
                }, 5000);
            }
        },

        /**
         * Show command output in a formatted way
         * @param {string} command - The command that was executed
         * @param {string} output - The command output
         */
        showOutput: function(command, output) {
            const html = '<div class="git-command-output">' +
                '<h4>Command: git ' + command + '</h4>' +
                '<pre>' + output + '</pre>' +
                '</div>';
            $('#git-output').html(html);
        },

        /**
         * Show diff output in a formatted way
         * @param {string} file - The file name
         * @param {string} diff - The diff content
         */
        showDiff: function(file, diff) {
            const html = '<div class="git-diff-output">' +
                '<h4>Diff for: ' + file + '</h4>' +
                '<pre class="diff-content">' + diff + '</pre>' +
                '</div>';
            $('#git-output').html(html);
        },

        /**
         * Show commit changes in a formatted way
         * @param {string} commitHash - The commit hash
         * @param {string} changes - The commit changes
         */
        showCommitChanges: function(commitHash, changes) {
            const shortHash = commitHash.substring(0, 8);
            const html = '<div class="git-commit-changes-output">' +
                '<h4>Changes in commit: ' + shortHash + '</h4>' +
                '<pre class="commit-changes-content">' + changes + '</pre>' +
                '</div>';
            $('#git-output').html(html);
        },

        /**
         * Confirm action with user
         * @param {string} message - Confirmation message
         * @param {function} callback - Callback to execute if confirmed
         */
        confirmAction: function(message, callback) {
            if (confirm(message)) {
                callback();
            }
        },

        /**
         * Prompt user for input
         * @param {string} message - Prompt message
         * @param {function} callback - Callback to execute with input
         * @param {string} defaultValue - Default input value
         */
        promptInput: function(message, callback, defaultValue) {
            const input = prompt(message, defaultValue || '');
            if (input !== null && input.trim()) {
                callback(input.trim());
            }
        },

        /**
         * Reload the current page
         */
        reloadPage: function() {
            location.reload();
        },

        /**
         * Refresh file status on manage page
         * @param {function} callback - Optional callback function
         */
        refreshFileStatus: function (callback) {
            if (typeof ManagePage !== 'undefined' && ManagePage.refreshFileStatus) {
                ManagePage.refreshFileStatus();
                if (callback) callback();
            } else {
                // Fallback to page reload if ManagePage is not available
                GitUtils.reloadPage();
            }
        },

        /**
         * Common Git operations
         */
        git: {
            /**
             * Stage a file
             * @param {string} file - File path to stage
             * @param {function} callback - Success callback
             */
            stageFile: function(file, callback) {
                GitUtils.ajaxRequest('git_stage_file', { file: file }, function(response) {
                    GitUtils.showMessage('File staged successfully', 'success');
                    GitUtils.refreshFileStatus();
                    if (callback) callback(response);
                }, null, 'Staging file...');
            },

            /**
             * Unstage a file
             * @param {string} file - File path to unstage
             * @param {function} callback - Success callback
             */
            unstageFile: function(file, callback) {
                GitUtils.ajaxRequest('git_unstage_file', { file: file }, function(response) {
                    GitUtils.showMessage('File unstaged successfully', 'success');
                    GitUtils.refreshFileStatus();
                    if (callback) callback(response);
                }, null, 'Unstaging file...');
            },

            /**
             * View file diff
             * @param {string} file - File path to view diff for
             */
            viewDiff: function(file) {
                GitUtils.ajaxRequest('git_file_diff', { file: file }, function(response) {
                    GitUtils.showDiff(file, response.data.diff);
                }, null, 'Loading diff...');
            },

            /**
             * Create a commit
             * @param {string} message - Commit message
             * @param {boolean} autoStage - Whether to auto-stage files
             * @param {function} callback - Success callback
             */
            commit: function(message, autoStage, callback) {
                GitUtils.ajaxRequest('git_commit', {
                    message: message,
                    auto_stage: autoStage
                }, function(response) {
                    GitUtils.showMessage('Commit created successfully', 'success');
                    GitUtils.refreshFileStatus();
                    if (callback) callback(response);
                }, null, 'Creating commit...');
            },

            /**
             * Push changes
             * @param {function} callback - Success callback
             */
            push: function(callback) {
                GitUtils.ajaxRequest('git_push', {}, function(response) {
                    GitUtils.showMessage('Push successful', 'success');
                    if (callback) callback(response);
                }, null, 'Pushing to remote...');
            },

            /**
             * Pull changes
             * @param {function} callback - Success callback
             */
            pull: function(callback) {
                GitUtils.ajaxRequest('git_pull', {}, function(response) {
                    GitUtils.showMessage('Pull successful', 'success');
                    if (callback) callback(response);
                    GitUtils.reloadPage();
                }, null, 'Pulling from remote...');
            }
        }
    };

})(jQuery);
