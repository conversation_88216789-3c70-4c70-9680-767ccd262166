<?php

/**
 * AJAX handlers class for WP Git Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WPGitManager_AjaxHandlers
{

    private $git_ops;

    public function __construct()
    {
        include_once plugin_dir_path(__FILE__) . 'class-git-operations.php';
        $this->git_ops = new WPGitManager_GitOperations();
    }

    /**
     * Base method to handle common AJAX security checks
     * @return bool True if security checks pass, false otherwise
     */
    private function verify_ajax_security()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        return true;
    }

    public function handle_commit()
    {
        $this->verify_ajax_security();

        $commit_message = sanitize_text_field($_POST['message']);

        if (empty($commit_message)) {
            wp_send_json_error('Commit message is required');
        }

        // Add all files
        $add_result = $this->git_ops->execute_command('add -A');
        if (!$add_result['success']) {
            wp_send_json_error('Failed to add files: ' . $add_result['message']);
        }

        // Commit changes
        $commit_result = $this->git_ops->execute_command('commit -m ' . escapeshellarg($commit_message));
        if ($commit_result['success'] || strpos($commit_result['message'], 'nothing to commit') !== false) {
            wp_send_json_success($commit_result['message']);
        } else {
            wp_send_json_error('Commit failed: ' . $commit_result['message']);
        }
    }

    public function handle_status()
    {
        $this->verify_ajax_security();

        $status = $this->git_ops->get_status();
        wp_send_json_success($status);
    }

    public function handle_push()
    {
        $this->verify_ajax_security();

        $remote_name = get_option('wpgm_remote_name', 'origin');
        $branch_name = get_option('wpgm_branch_name', 'main');

        $push_result = $this->git_ops->execute_command('push ' . escapeshellarg($remote_name) . ' ' . escapeshellarg($branch_name));

        if ($push_result['success']) {
            wp_send_json_success($push_result['message']);
        } else {
            wp_send_json_error('Push failed: ' . $push_result['message']);
        }
    }

    public function handle_pull()
    {
        $this->verify_ajax_security();

        $remote_name = get_option('wpgm_remote_name', 'origin');
        $branch_name = get_option('wpgm_branch_name', 'main');

        $pull_result = $this->git_ops->execute_command('pull ' . escapeshellarg($remote_name) . ' ' . escapeshellarg($branch_name));

        if ($pull_result['success']) {
            wp_send_json_success($pull_result['message']);
        } else {
            wp_send_json_error('Pull failed: ' . $pull_result['message']);
        }
    }

    public function handle_setup_check()
    {
        $this->verify_ajax_security();

        $check_type = sanitize_text_field($_POST['check_type']);

        switch ($check_type) {
            case 'git_path':
                $git_path = sanitize_text_field($_POST['git_path']);
                $result = $this->git_ops->test_git_path($git_path);

                if ($result['success']) {
                    wp_send_json_success($result);
                } else {
                    wp_send_json_error($result['message']);
                }
                break;

            case 'full_status':
                $git_path = sanitize_text_field($_POST['git_path']);
                $repo_path = sanitize_text_field($_POST['repo_path']);

                // Save paths for future use
                update_option('wpgm_git_path', $git_path);
                update_option('wpgm_repo_path', $repo_path);

                $status = $this->git_ops->check_setup_status();
                wp_send_json_success($status);
                break;

            case 'save_settings':
                $settings = $_POST['settings'];

                // Handle gitignore content separately
                if (isset($settings['gitignore_content'])) {
                    $gitignore_content = sanitize_textarea_field($settings['gitignore_content']);
                    $repo_path = get_option('wpgm_repo_path', ABSPATH);
                    $gitignore_file = rtrim($repo_path, '/') . '/.gitignore';

                    if (!empty($gitignore_content)) {
                        file_put_contents($gitignore_file, $gitignore_content);
                    }

                    unset($settings['gitignore_content']);
                }

                foreach ($settings as $key => $value) {
                    update_option('wpgm_' . $key, sanitize_text_field($value));
                }
                update_option('wpgm_needs_setup', false);
                wp_send_json_success('Settings saved');
                break;
        }

        wp_send_json_error('Invalid check type');
    }

    public function handle_init_repo()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);

        $result = $this->git_ops->init_repository($git_path, $repo_path);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_set_user()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);
        $user_name = sanitize_text_field($_POST['user_name']);
        $user_email = sanitize_email($_POST['user_email']);

        $result = $this->git_ops->set_user_config($git_path, $repo_path, $user_name, $user_email);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_add_remote()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);
        $remote_url = esc_url_raw($_POST['remote_url']);
        $remote_name = sanitize_text_field($_POST['remote_name']);

        $result = $this->git_ops->add_remote($git_path, $repo_path, $remote_name, $remote_url);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_create_branch()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);
        $branch_name = sanitize_text_field($_POST['branch_name']);

        if (empty($branch_name)) {
            wp_send_json_error('Branch name is required');
        }

        $result = $this->git_ops->create_branch($git_path, $repo_path, $branch_name);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_init_repo_with_branch()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);
        $branch_name = sanitize_text_field($_POST['branch_name']);

        if (empty($branch_name)) {
            wp_send_json_error('Branch name is required');
        }

        $result = $this->git_ops->init_repository_with_branch($git_path, $repo_path, $branch_name);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_create_initial_commit()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);
        $branch_name = sanitize_text_field($_POST['branch_name']);

        $result = $this->git_ops->create_initial_commit($git_path, $repo_path, $branch_name);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_git_command()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $git_command = sanitize_text_field($_POST['git_command']);

        // Log the command for debugging
        error_log('WP Git Manager: Executing git command: ' . $git_command);

        $result = $this->git_ops->execute_command($git_command);

        // Log the result for debugging
        error_log('WP Git Manager: Command result: ' . print_r($result, true));

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_commit_changes()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $commit_hash = sanitize_text_field($_POST['commit_hash']);

        if (empty($commit_hash)) {
            wp_send_json_error('Commit hash is required');
        }

        $result = $this->git_ops->get_commit_changes($commit_hash);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    // Enhanced Git features handlers

    public function handle_stage_file()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $file_path = sanitize_text_field($_POST['file']);
        $result = $this->git_ops->stage_file($file_path);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_unstage_file()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $file_path = sanitize_text_field($_POST['file']);
        $result = $this->git_ops->unstage_file($file_path);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_file_diff()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $file_path = sanitize_text_field($_POST['file']);
        $result = $this->git_ops->get_file_diff($file_path);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_commit_history()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
        $result = $this->git_ops->get_commit_history($limit);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_switch_branch()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $branch_name = sanitize_text_field($_POST['branch']);
        $result = $this->git_ops->switch_branch($branch_name);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_get_branches()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $result = $this->git_ops->get_branches();

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle Git repository reset request
     */
    public function handle_reset_repository()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        // Double confirmation required for this destructive action
        $confirm = sanitize_text_field($_POST['confirm']);
        if ($confirm !== 'RESET_REPOSITORY') {
            wp_send_json_error('Confirmation required. Please type "RESET_REPOSITORY" to confirm.');
        }

        $repo_path = isset($_POST['repo_path']) ? sanitize_text_field($_POST['repo_path']) : null;
        $clear_all_data = isset($_POST['clear_all_data']) && $_POST['clear_all_data'] === 'true';

        $result = $this->git_ops->reset_repository($repo_path, $clear_all_data);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle repository information request
     */
    public function handle_get_repository_info()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $info = $this->git_ops->get_repository_info();

        // Format the size for display
        if ($info['size'] > 0) {
            $info['size_formatted'] = $this->git_ops->format_bytes($info['size']);
        } else {
            $info['size_formatted'] = '0 B';
        }

        wp_send_json_success($info);
    }

    public function handle_stage_all()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $result = $this->git_ops->execute_command('add -A');

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_unstage_all()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $result = $this->git_ops->execute_command('reset HEAD');

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_get_remotes()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $git_path = get_option('wpgm_git_path', '/usr/bin/git');
        $repo_path = get_option('wpgm_repo_path', ABSPATH);
        $remotes = $this->git_ops->get_remotes($git_path, $repo_path);

        wp_send_json_success($remotes);
    }

    /**
     * Handle refresh file status request
     * Returns HTML for the file status table
     */
    public function handle_refresh_file_status()
    {
        $this->verify_ajax_security();

        $git_path = get_option('wpgm_git_path', '/usr/bin/git');
        $repo_path = get_option('wpgm_repo_path', ABSPATH);
        $status = $this->git_ops->get_status($git_path, $repo_path);

        // Generate the HTML for the file status table
        ob_start();
        $this->render_file_status_table($status);
        $html = ob_get_clean();

        wp_send_json_success(array(
            'html' => $html,
            'files_count' => count($status['files'] ?? []),
            'changes_count' => $status['changes_count'] ?? 0
        ));
    }

    /**
     * Render the file status table HTML
     * @param array $status Git status information
     */
    private function render_file_status_table($status)
    {
        if (!empty($status['files'])): ?>
            <div class="git-bulk-actions" style="margin-bottom: 10px;">
                <button type="button" class="button" id="stage-selected-btn" disabled>
                    <span class="dashicons dashicons-plus"></span> Stage Selected Files
                </button>
                <button type="button" class="button" id="unstage-selected-btn" disabled>
                    <span class="dashicons dashicons-minus"></span> Unstage Selected Files
                </button>
                <span id="selected-count" style="margin-left: 10px; color: #666;"></span>
            </div>

            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col" class="manage-column column-cb check-column">
                            <input type="checkbox" id="select-all-files">
                        </th>
                        <th scope="col" class="manage-column">Status</th>
                        <th scope="col" class="manage-column">File</th>
                        <th scope="col" class="manage-column">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($status['files'] as $file):
                        $raw_status = $file['raw_status'] ?? '  ';
                        $is_staged = ($raw_status[0] !== ' ' && $raw_status[0] !== '?');
                        $is_modified = ($raw_status[1] !== ' ');
                    ?>
                        <tr>
                            <th scope="row" class="check-column">
                                <input type="checkbox" name="selected_files[]" value="<?php echo esc_attr($file['name']); ?>" class="file-checkbox">
                            </th>
                            <td>
                                <div class="file-status-container">
                                    <span class="file-status status-<?php echo esc_attr(strtolower($file['status'])); ?>">
                                        <?php echo esc_html($file['status']); ?>
                                    </span>
                                    <?php if ($is_staged): ?>
                                        <span class="staging-badge staged" title="File is staged for commit">
                                            <span class="dashicons dashicons-yes-alt"></span> Staged
                                        </span>
                                    <?php else: ?>
                                        <span class="staging-badge unstaged" title="File is not staged">
                                            <span class="dashicons dashicons-clock"></span> Unstaged
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <code><?php echo esc_html($file['name']); ?></code>
                            </td>
                            <td>
                                <?php if (!$is_staged): ?>
                                    <button type="button" class="button button-small git-stage-file" data-file="<?php echo esc_attr($file['name']); ?>">
                                        <span class="dashicons dashicons-plus"></span> Stage
                                    </button>
                                <?php else: ?>
                                    <button type="button" class="button button-small git-unstage-file" data-file="<?php echo esc_attr($file['name']); ?>">
                                        <span class="dashicons dashicons-minus"></span> Unstage
                                    </button>
                                <?php endif; ?>
                                <button type="button" class="button button-small git-view-diff" data-file="<?php echo esc_attr($file['name']); ?>">
                                    <span class="dashicons dashicons-visibility"></span> View Diff
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="notice notice-info">
                <p>No changes detected in the repository.</p>
            </div>
<?php endif;
    }

    /**
     * Handle staging selected files
     */
    public function handle_stage_selected_files()
    {
        $this->verify_ajax_security();

        $files = $_POST['files'] ?? array();
        if (empty($files) || !is_array($files)) {
            wp_send_json_error('No files selected');
        }

        $git_path = get_option('wpgm_git_path', '/usr/bin/git');
        $repo_path = get_option('wpgm_repo_path', ABSPATH);

        $staged_files = array();
        $failed_files = array();

        foreach ($files as $file) {
            $result = $this->git_ops->stage_file($file);
            if ($result['success']) {
                $staged_files[] = $file;
            } else {
                $failed_files[] = array('file' => $file, 'error' => $result['message']);
            }
        }

        $message = sprintf('Staged %d file(s) successfully', count($staged_files));
        if (!empty($failed_files)) {
            $message .= sprintf(', %d file(s) failed', count($failed_files));
        }

        wp_send_json_success(array(
            'message' => $message,
            'staged_files' => $staged_files,
            'failed_files' => $failed_files,
            'staged_count' => count($staged_files),
            'failed_count' => count($failed_files)
        ));
    }

    /**
     * Handle unstaging selected files
     */
    public function handle_unstage_selected_files()
    {
        $this->verify_ajax_security();

        $files = $_POST['files'] ?? array();
        if (empty($files) || !is_array($files)) {
            wp_send_json_error('No files selected');
        }

        $git_path = get_option('wpgm_git_path', '/usr/bin/git');
        $repo_path = get_option('wpgm_repo_path', ABSPATH);

        $unstaged_files = array();
        $failed_files = array();

        foreach ($files as $file) {
            $result = $this->git_ops->unstage_file($file);
            if ($result['success']) {
                $unstaged_files[] = $file;
            } else {
                $failed_files[] = array('file' => $file, 'error' => $result['message']);
            }
        }

        $message = sprintf('Unstaged %d file(s) successfully', count($unstaged_files));
        if (!empty($failed_files)) {
            $message .= sprintf(', %d file(s) failed', count($failed_files));
        }

        wp_send_json_success(array(
            'message' => $message,
            'unstaged_files' => $unstaged_files,
            'failed_files' => $failed_files,
            'unstaged_count' => count($unstaged_files),
            'failed_count' => count($failed_files)
        ));
    }
}
