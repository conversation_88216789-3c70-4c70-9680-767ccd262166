<?php
/**
 * Plugin Name: WP Git Manager 2.0
 * Plugin URI: https://yourwebsite.com/wp-git-manager
 * Description: Adds Git functionality to WordPress admin bar for quick commits and repository management.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load Composer autoloader
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
}

class WPGitManager {
    
    private $plugin_url;
    private $plugin_path;
    
    public function __construct() {
        $this->plugin_url = plugin_dir_url(__FILE__);
        $this->plugin_path = plugin_dir_path(__FILE__);
        
        add_action('init', array($this, 'init'));
        add_action('admin_bar_menu', array($this, 'add_admin_bar_menu'), 100);
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_notices', array($this, 'admin_notices'));

        // Register AJAX handlers
        $this->register_ajax_handlers();
    }

    /**
     * Register all AJAX handlers using a centralized approach
     */
    private function register_ajax_handlers()
    {
        $ajax_actions = array(
            'git_commit',
            'git_status',
            'git_push',
            'git_pull',
            'git_setup_check',
            'git_init_repo',
            'git_add_remote',
            'git_create_branch',
            'git_set_user',
            'git_command',
            'git_init_repo_with_branch',
            'git_create_initial_commit',
            'git_stage_file',
            'git_unstage_file',
            'git_file_diff',
            'git_commit_history',
            'git_switch_branch',
            'git_get_branches',
            'git_reset_repository',
            'git_get_repository_info',
            'git_stage_all',
            'git_unstage_all',
            'git_get_remotes',
            'git_commit_changes',
            'git_refresh_file_status',
            'git_stage_selected_files',
            'git_unstage_selected_files'
        );

        foreach ($ajax_actions as $action) {
            add_action('wp_ajax_' . $action, array($this, 'handle_ajax_request'));
        }
    }

    /**
     * Generic AJAX handler that delegates to the appropriate method in the AJAX handlers class
     */
    public function handle_ajax_request()
    {
        $action = str_replace('wp_ajax_', '', current_action());
        $method_name = 'handle_' . str_replace('git_', '', $action);

        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();

        if (method_exists($ajax_handler, $method_name)) {
            $ajax_handler->$method_name();
        } else {
            wp_send_json_error('Invalid AJAX action: ' . $action);
        }
    }

    public function init() {
        // Check if setup is needed on admin pages
        if (is_admin() && current_user_can('manage_options')) {
            $setup_status = $this->check_setup_status();
            if (!$setup_status['is_complete']) {
                update_option('wpgm_needs_setup', true);
            }
        }
    }
    
    public function admin_notices() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        $needs_setup = get_option('wpgm_needs_setup', false);
        if ($needs_setup) {
            $setup_url = admin_url('admin.php?page=wp-git-manager-setup');
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>WP Git Manager:</strong> Git repository setup is incomplete. ';
            echo '<a href="' . esc_url($setup_url) . '">Complete setup now</a></p>';
            echo '</div>';
        }
    }
    
    public function add_admin_bar_menu($wp_admin_bar) {
        // Only show to users who can manage options and if setup is complete
        if (!current_user_can('manage_options') || get_option('wpgm_needs_setup', false)) {
            return;
        }
        
        $git_status = $this->get_git_status();
        $changes_count = $git_status['changes_count'] ?? 0;
        
        $title = 'Git';
        if ($changes_count > 0) {
            $title .= ' (' . $changes_count . ')';
        }
        
        $wp_admin_bar->add_menu(array(
            'id' => 'wp-git-manager',
            'title' => $title,
            'href' => '#',
            'meta' => array(
                'class' => 'wp-git-manager-button'
            )
        ));
        
        // Add submenu items
        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-commit',
            'title' => 'Commit Changes',
            'href' => '#',
            'meta' => array(
                'class' => 'git-commit-btn'
            )
        ));
        
        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-push',
            'title' => 'Push',
            'href' => '#',
            'meta' => array(
                'class' => 'git-push-btn'
            )
        ));
        
        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-pull',
            'title' => 'Pull',
            'href' => '#',
            'meta' => array(
                'class' => 'git-pull-btn'
            )
        ));

        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-dashboard',
            'title' => 'Dashboard',
            'href' => admin_url('admin.php?page=wp-git-manager')
        ));

        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-manage',
            'title' => 'Manage',
            'href' => admin_url('admin.php?page=wp-git-manager-manage')
        ));

        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-settings',
            'title' => 'Settings',
            'href' => admin_url('admin.php?page=wp-git-manager-settings')
        ));
    }
    
    public function add_admin_menu() {
        // Add main Git Manager menu
        add_menu_page(
            'Git Manager',
            'Git Manager',
            'manage_options',
            'wp-git-manager',
            array($this, 'dashboard_page'),
            'dashicons-admin-tools',
            30
        );

        // Add Dashboard submenu (default)
        add_submenu_page(
            'wp-git-manager',
            'Git Manager Dashboard',
            'Dashboard',
            'manage_options',
            'wp-git-manager',
            array($this, 'dashboard_page')
        );

        // Add Manage submenu
        add_submenu_page(
            'wp-git-manager',
            'Git Manager - Manage',
            'Manage',
            'manage_options',
            'wp-git-manager-manage',
            array($this, 'manage_page')
        );

        // Add Settings submenu
        add_submenu_page(
            'wp-git-manager',
            'Git Manager Settings',
            'Settings',
            'manage_options',
            'wp-git-manager-settings',
            array($this, 'settings_page')
        );

        // Add Setup submenu (hidden from menu but accessible via URL)
        add_submenu_page(
            null, // null parent means it won't show in menu but is accessible
            'Git Manager Setup',
            'Setup',
            'manage_options',
            'wp-git-manager-setup',
            array($this, 'setup_page')
        );
    }
    
    public function enqueue_scripts() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Enqueue utility functions first
        wp_enqueue_script(
            'wp-git-manager-utils',
            $this->plugin_url . 'assets/git-utils.js',
            array('jquery'),
            '1.0.0',
            true
        );

        wp_enqueue_script(
            'wp-git-manager-main',
            $this->plugin_url . 'assets/wp-git-manager.js',
            array('jquery', 'wp-git-manager-utils'),
            '1.0.0',
            true
        );
        
        wp_enqueue_style(
            'wp-git-manager-css',
            $this->plugin_url . 'assets/wp-git-manager.css',
            array(),
            '1.0.0'
        );

        wp_localize_script('wp-git-manager-main', 'wpGitManager', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'admin_url' => admin_url(),
            'nonce' => wp_create_nonce('wp_git_manager_nonce'),
            'strings' => array(
                'commit_message' => 'Enter commit message:',
                'commit_success' => 'Changes committed successfully!',
                'commit_error' => 'Error committing changes.',
                'push_success' => 'Changes pushed successfully!',
                'push_error' => 'Error pushing changes.',
                'pull_success' => 'Repository updated successfully!',
                'pull_error' => 'Error pulling changes.',
                'no_changes' => 'No changes to commit.',
                'confirm_push' => 'Are you sure you want to push changes?',
                'confirm_pull' => 'Are you sure you want to pull changes? This may overwrite local changes.',
                'setup_checking' => 'Checking Git setup...',
                'setup_error' => 'Setup check failed.',
                'initializing' => 'Initializing...',
                'success' => 'Success!',
                'error' => 'Error occurred.'
            )
        ));
    }
    
    // Include Git operations class
    private function get_git_status() {
        include_once $this->plugin_path . 'includes/class-git-operations.php';
        $git_ops = new WPGitManager_GitOperations();
        return $git_ops->get_status();
    }



    private function check_setup_status() {
        include_once $this->plugin_path . 'includes/class-git-operations.php';
        $git_ops = new WPGitManager_GitOperations();
        return $git_ops->check_setup_status();
    }







    // Dashboard page
    public function dashboard_page()
    {
        include $this->plugin_path . 'admin/dashboard-page.php';
    }

    // Manage page
    public function manage_page()
    {
        include $this->plugin_path . 'admin/manage-page.php';
    }

    // Settings page
    public function settings_page() {
        include $this->plugin_path . 'admin/settings-page.php';
    }

    // Setup page (for internal use)
    public function setup_page()
    {
        include $this->plugin_path . 'admin/setup-page.php';
    }
}

// Initialize the plugin
new WPGitManager();